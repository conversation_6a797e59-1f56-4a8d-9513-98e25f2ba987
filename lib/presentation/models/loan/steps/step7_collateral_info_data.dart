import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sales_app/core/enums/enums.dart';

part 'step7_collateral_info_data.freezed.dart';

/// Model cho data của Step 7 - Thông tin tài sản bảo đảm
@freezed
class Step7CollateralInfoData with _$Step7CollateralInfoData {
  const factory Step7CollateralInfoData({
    @Default('') String assetType,
    @Default(0) int assetValue,
    @Default('') String assetValueInWords,
    @Default(AssetCondition.inUse) AssetCondition assetCondition,
    @Default('') String ownerName,
    required DateTime ownerBirthDate,
    @Default('') String registrationType,
    @Default('') String assetLocation, // For collateral address
  }) = _Step7CollateralInfoData;

  // Custom getter
  const Step7CollateralInfoData._();
  
  /// Kiểm tra step đã hoàn thành chưa
  bool get isComplete {
    if (assetType.trim().isEmpty) return false;
    if (assetValue <= 0) return false;
    if (assetValueInWords.trim().isEmpty) return false;
    if (ownerName.trim().isEmpty) return false;
    if (registrationType.trim().isEmpty) return false;
    if (assetLocation.trim().isEmpty) return false;
    return true;
  }

  /// Kiểm tra có lỗi không
  bool get hasError => false; // Can be extended later

  /// Kiểm tra thông tin cơ bản đã đầy đủ chưa
  bool get hasBasicInfo {
    return assetType.trim().isNotEmpty &&
           assetValue > 0 &&
           assetValueInWords.trim().isNotEmpty;
  }

  /// Kiểm tra thông tin chủ sở hữu đã đầy đủ chưa  
  bool get hasOwnerInfo {
    return ownerName.trim().isNotEmpty &&
           registrationType.trim().isNotEmpty;
  }

  /// Kiểm tra địa chỉ tài sản đã đầy đủ chưa
  bool get hasLocationInfo {
    return assetLocation.trim().isNotEmpty;
  }

  /// Kiểm tra giá trị tài sản có hợp lý không
  bool get hasReasonableValue {
    if (assetValue <= 0) return false;
    if (assetValue > 1000000000000) return false; // Max 1 trillion VND
    return true;
  }
}