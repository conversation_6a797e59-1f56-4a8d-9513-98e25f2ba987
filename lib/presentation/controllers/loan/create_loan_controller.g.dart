// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'create_loan_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$createLoanControllerHash() =>
    r'1443ba4085bbe9deffd24fea5bb5a7b77d62a595';

/// Controller quản lý toàn bộ flow tạo khoản vay
///
/// Flow: identityDocument → borrowerInfo → coBorrowerDocument → coBorrowerInfo →
/// loanRequest → financialInfo → collateralInfo → collateralDetail → documentList →
/// loanConfirmation → success
///
/// Copied from [CreateLoanController].
@ProviderFor(CreateLoanController)
final createLoanControllerProvider = AutoDisposeNotifierProvider<
    CreateLoanController, BaseState<CreateLoanFlowData>>.internal(
  CreateLoanController.new,
  name: r'createLoanControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$createLoanControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CreateLoanController
    = AutoDisposeNotifier<BaseState<CreateLoanFlowData>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
